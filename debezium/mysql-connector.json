{"name": "mysql-connector", "config": {"connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.hostname": "mysql", "database.port": "3306", "database.user": "dbuser", "database.password": "dbpassword", "database.server.id": "184054", "database.include.list": "testdb", "table.include.list": "testdb.orders", "topic.prefix": "dbserver1", "schema.history.internal.kafka.bootstrap.servers": "kafka:9092", "schema.history.internal.kafka.topic": "schema-changes.testdb", "config.storage.topic": "my_connect_configs", "offset.storage.topic": "my_connect_offsets", "status.storage.topic": "my_connect_statuses"}}