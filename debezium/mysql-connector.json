{"name": "mysql-connector", "config": {"connector.class": "io.debezium.connector.mysql.MySqlConnector", "database.hostname": "mysql", "database.port": "3306", "database.user": "dbuser", "database.password": "dbpassword", "database.server.id": "184054", "database.include.list": "testdb", "table.include.list": "testdb.users,testdb.products,testdb.orders", "topic.prefix": "dbserver1", "schema.history.internal.kafka.bootstrap.servers": "kafka:9092", "schema.history.internal.kafka.topic": "schema-changes.testdb", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "false", "value.converter.schemas.enable": "false", "include.schema.changes": "false", "snapshot.mode": "initial", "snapshot.locking.mode": "minimal", "decimal.handling.mode": "string"}}