# MySQL-Debezium-Kafka-Typesense Data Pipeline

## Architecture Flow
MySQL Database → Debezium → Kafka → Python Consumer → Typesense

## Project Structure
```
.
├── docker-compose.yml          # Main Docker orchestration file
├── mysql/
│   ├── init.sql               # Database initialization script
│   └── my.cnf                 # MySQL configuration for CDC
├── debezium/
│   └── mysql-connector.json   # Debezium connector configuration
├── python/
│   ├── requirements.txt       # Python dependencies
│   ├── kafka_consumer.py      # Main Kafka consumer application
│   ├── typesense_sync.py      # Typesense synchronization service
│   └── config.py              # Configuration settings
├── scripts/
│   ├── setup.sh              # Setup and initialization script
│   ├── test_pipeline.py      # Pipeline testing script
│   └── cleanup.sh            # Cleanup script
└── README.md                 # This file
```

## Quick Start
```sh
chmod +x scripts/setup.sh && ./scripts/setup.sh
docker-compose up -d
# Wait for all services to start (check with docker-compose ps)
python scripts/test_pipeline.py
```

## Detailed Workflow Explanation

### 🔄 Complete Data Flow
MySQL Database Changes → Debezium CDC → Kafka Topics → Python Consumer → Typesense Search

---

## Basic Usage
- Make changes in MySQL (insert/update/delete)
- Debezium streams changes to Kafka
- Python consumer syncs to Typesense

## Notes
- Ensure Docker and Docker Compose are installed
- Default ports: 3306 (MySQL), 8083 (Debezium), 9092 (Kafka), 8108 (Typesense)
- Replace passwords and API keys as needed for your setup

---
For advanced usage, troubleshooting, or contributing, see the full documentation online or in the original README history.