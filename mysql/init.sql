USE testdb;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    age INT,
    city VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(50) NOT NULL,
    stock_quantity INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    product_id INT,
    quantity INT NOT NULL DEFAULT 1,
    item_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    order_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
);

-- Insert sample data
INSERT INTO users (username, email, full_name, age, city) VALUES
('john_doe', '<EMAIL>', 'John Doe', 30, 'New York'),
('jane_smith', '<EMAIL>', 'Jane Smith', 25, 'Los Angeles'),
('bob_wilson', '<EMAIL>', 'Bob Wilson', 35, 'Chicago');

INSERT INTO products (name, description, price, category, stock_quantity) VALUES
('iPhone 15', 'Latest iPhone model with advanced features', 999.99, 'Electronics', 50),
('MacBook Pro', 'High-performance laptop for professionals', 1999.99, 'Electronics', 25),
('Nike Air Max', 'Comfortable running shoes', 129.99, 'Footwear', 100),
('Coffee Mug', 'Ceramic coffee mug', 12.99, 'Home', 200);

INSERT INTO orders (user_id, product_id, quantity, item_price, total_price, order_status) VALUES
(1, 1, 1, 999.99, 999.99, 'completed'),
(2, 3, 2, 129.99, 259.98, 'pending'),
(3, 2, 1, 1999.99, 1999.99, 'shipped');

-- Grant necessary permissions for Debezium
GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'dbuser'@'%';
FLUSH PRIVILEGES;

SHOW MASTER STATUS;