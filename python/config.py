"""
Configuration module for MySQL-Debezium-Kafka-Typesense Pipeline

This module centralizes all configuration settings and explains why each setting is needed.
Environment variables are used to make the application configurable across different environments.
"""

import os
from typing import Dict

class Config:
    """
    Configuration class that loads settings from environment variables.

    Why use environment variables?
    - Keeps sensitive data (passwords, API keys) out of source code
    - Allows different configurations for dev/staging/production
    - Follows 12-factor app methodology
    """

    # KAFKA CONFIGURATION
    # These settings control how we connect to and consume from Kafka

    # KAFKA_BOOTSTRAP_SERVERS: Comma-separated list of Kafka broker addresses
    # Why needed: Tells Kafka client where to find the Kafka cluster
    KAFKA_BOOTSTRAP_SERVERS = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:9092')

    # KAFKA_CONSUMER_GROUP: Consumer group ID for this application
    # Why needed: Ka<PERSON><PERSON> uses consumer groups to distribute messages and track offsets
    KAFKA_CONSUMER_GROUP = os.getenv('KAFKA_CONSUMER_GROUP', 'typesense-sync-group')

    # KAFKA_AUTO_OFFSET_RESET: What to do when no offset exists
    # 'earliest' = start from beginning, 'latest' = start from end
    # Why needed: Determines behavior on first run or after offset loss
    KAFKA_AUTO_OFFSET_RESET = os.getenv('KAFKA_AUTO_OFFSET_RESET', 'earliest')

    # KAFKA_TOPICS: List of Kafka topics to consume from
    # Why needed: These are the topics created by Debezium for our tables
    KAFKA_TOPICS = [
        'dbserver1.testdb.orders'      # Topic for orders table changes
    ]

    # TYPESENSE CONFIGURATION
    # These settings control connection to Typesense search engine

    # TYPESENSE_HOST: Hostname/IP of Typesense server
    # Why needed: Tells client where to find Typesense API
    TYPESENSE_HOST = os.getenv('TYPESENSE_HOST', 'typesense')

    # TYPESENSE_PORT: Port number for Typesense API
    # Why needed: Typesense default port is 8108
    TYPESENSE_PORT = int(os.getenv('TYPESENSE_PORT', '8108'))

    # TYPESENSE_API_KEY: Authentication key for Typesense
    # Why needed: Typesense requires API key for all operations
    TYPESENSE_API_KEY = os.getenv('TYPESENSE_API_KEY', 'xyz123')

    # TYPESENSE_PROTOCOL: HTTP or HTTPS
    # Why needed: Determines connection security (HTTP for local dev)
    TYPESENSE_PROTOCOL = os.getenv('TYPESENSE_PROTOCOL', 'http')

    # APPLICATION CONFIGURATION
    # These settings control application behavior

    # LOG_LEVEL: Logging verbosity (DEBUG, INFO, WARNING, ERROR)
    # Why needed: Controls how much detail appears in logs
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

    # RETRY_ATTEMPTS: Number of times to retry failed operations
    # Why needed: Handle transient network/service failures
    RETRY_ATTEMPTS = int(os.getenv('RETRY_ATTEMPTS', '3'))

    # RETRY_DELAY: Seconds to wait between retry attempts
    # Why needed: Avoid overwhelming services during failures
    RETRY_DELAY = int(os.getenv('RETRY_DELAY', '5'))

    @classmethod
    def get_kafka_config(cls) -> Dict:
        """
        Returns Kafka consumer configuration dictionary.

        Why separate method?
        - Encapsulates Kafka-specific settings
        - Makes it easy to pass to KafkaConsumer constructor
        """
        return {
            'bootstrap_servers': cls.KAFKA_BOOTSTRAP_SERVERS.split(','),
            'group_id': cls.KAFKA_CONSUMER_GROUP,
            'auto_offset_reset': cls.KAFKA_AUTO_OFFSET_RESET,
            'enable_auto_commit': True,  # Automatically commit offsets
            'value_deserializer': lambda x: x.decode('utf-8'),  # Decode bytes to string
            'consumer_timeout_ms': 1000,  # Timeout for polling messages
        }

    @classmethod
    def get_typesense_config(cls) -> Dict:
        """
        Returns Typesense client configuration dictionary.

        Why separate method?
        - Encapsulates Typesense-specific settings
        - Makes it easy to pass to Typesense client constructor
        """
        return {
            'nodes': [{
                'host': cls.TYPESENSE_HOST,
                'port': cls.TYPESENSE_PORT,
                'protocol': cls.TYPESENSE_PROTOCOL
            }],
            'api_key': cls.TYPESENSE_API_KEY,
            'connection_timeout_seconds': 10
        }

    @classmethod
    def validate_config(cls) -> bool:
        """
        Validates that all required configuration is present.

        Why needed?
        - Fail fast if configuration is missing
        - Provide clear error messages for misconfiguration
        """
        required_vars = [
            'KAFKA_BOOTSTRAP_SERVERS',
            'TYPESENSE_HOST',
            'TYPESENSE_API_KEY'
        ]

        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var, None):
                missing_vars.append(var)

        if missing_vars:
            print(f"Missing required configuration: {', '.join(missing_vars)}")
            return False

        return True