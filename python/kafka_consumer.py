"""
Kafka Consumer for MySQL-Debezium-Kafka-Typesense Pipeline

This module consumes change events from Kafka topics created by <PERSON><PERSON><PERSON><PERSON>
and processes them for synchronization with Typesense.

WORKFLOW EXPLANATION:
1. Debezium captures MySQL changes → Kafka topics
2. This consumer reads from Kafka topics
3. Processes change events (INSERT, UPDATE, DELETE)
4. Sends processed data to Typesense via typesense_sync module

WHY KAFKA CONSUMER IS NEEDED:
- Decouples database changes from search index updates
- Provides fault tolerance and replay capability
- Enables horizontal scaling of processing
- Handles backpressure when Typesense is slow
"""

import json
import logging
import signal
import sys
from kafka import KafkaConsumer
from kafka.errors import KafkaError
from tenacity import retry, stop_after_attempt, wait_exponential

from config import Config
from typesense_sync import TypesenseSync

# Configure logging
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class DebeziumKafkaConsumer:
    """
    Main Kafka consumer class that processes Debezium change events.

    This class handles:
    - Connecting to Kafka cluster
    - Consuming messages from Debezium topics
    - Parsing Debezium change event format
    - Routing events to appropriate handlers
    - Error handling and retries
    """

    def __init__(self):
        """
        Initialize the Kafka consumer and Typesense sync handler.

        Why separate initialization?
        - Allows validation before starting consumption
        - Makes testing easier by mocking dependencies
        """
        self.consumer = None
        self.typesense_sync = TypesenseSync()
        self.running = False

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """
        Handle shutdown signals gracefully.

        Why needed?
        - Ensures consumer commits offsets before shutdown
        - Prevents data loss during container restarts
        - Allows clean resource cleanup
        """
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(min=Config.RETRY_DELAY)
    )
    def _create_consumer(self):
        """
        Create and configure Kafka consumer with retry logic.

        Why retry logic?
        - Kafka might not be ready when container starts
        - Network issues can cause temporary connection failures
        - Improves reliability in distributed environments
        """
        try:
            logger.info("Creating Kafka consumer...")
            consumer = KafkaConsumer(
                *Config.KAFKA_TOPICS,
                **Config.get_kafka_config()
            )
            logger.info(f"Successfully connected to Kafka, subscribed to topics: {Config.KAFKA_TOPICS}")
            return consumer

        except KafkaError as e:
            logger.error(f"Failed to create Kafka consumer: {e}")
            raise

    def parse_debezium_event(self, message_value):
        """
        Parse Debezium change event message.

        DEBEZIUM MESSAGE FORMAT:
        {
            "before": {...},     # Row data before change (null for INSERT)
            "after": {...},      # Row data after change (null for DELETE)
            "source": {...},     # Metadata about the change
            "op": "c|u|d|r",    # Operation: create, update, delete, read
            "ts_ms": 1234567890  # Timestamp in milliseconds
        }

        Why parse this format?
        - Debezium provides rich metadata about changes
        - Need to extract actual data from "before"/"after" fields
        - Operation type determines how to update Typesense
        """
        try:
            event = json.loads(message_value)

            # Extract key information from Debezium event
            operation = event.get('op')
            before_data = event.get('before')
            after_data = event.get('after')
            source_info = event.get('source', {})
            timestamp = event.get('ts_ms')

            # Log the event for debugging
            logger.debug(f"Parsed event - Operation: {operation}, Table: {source_info.get('table')}")

            return {
                'operation': operation,
                'before': before_data,
                'after': after_data,
                'table': source_info.get('table'),
                'database': source_info.get('db'),
                'timestamp': timestamp
            }

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON message: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error parsing message: {e}")
            return None

    def process_change_event(self, parsed_event):
        """
        Process a single change event and sync to Typesense.

        OPERATION TYPES:
        - 'c' (CREATE/INSERT): New record added
        - 'u' (UPDATE): Existing record modified
        - 'd' (DELETE): Record removed
        - 'r' (READ): Initial snapshot read

        Why different handling for each operation?
        - INSERT/READ: Add new document to Typesense
        - UPDATE: Update existing document in Typesense
        - DELETE: Remove document from Typesense
        """
        table_name = parsed_event['table']

        # Only process tables we care about
        if table_name not in ['users', 'products', 'orders']:
            logger.debug(f"Ignoring table: {table_name}")
            return False

        op = parsed_event['operation']
        logger.info(f"Processing {op} operation for table {table_name}")

        if op in ['c', 'r'] and parsed_event['after']:
            return self.typesense_sync.upsert_document(table_name, parsed_event['after'])
        elif op == 'u' and parsed_event['after']:
            return self.typesense_sync.upsert_document(table_name, parsed_event['after'])
        elif op == 'd' and parsed_event['before']:
            return self.typesense_sync.delete_document(table_name, str(parsed_event['before']['id']))

        return False

    def start_consuming(self):
        """
        Main consumption loop that processes messages from Kafka.

        CONSUMPTION FLOW:
        1. Create Kafka consumer connection
        2. Poll for messages from subscribed topics
        3. Parse each message as Debezium event
        4. Process event and sync to Typesense
        5. Handle errors and continue processing

        Why continuous loop?
        - Kafka is a streaming platform, messages arrive continuously
        - Need to process messages as they arrive for real-time sync
        - Loop allows handling of connection failures and retries
        """
        self.consumer = self._create_consumer()
        self.running = True

        while self.running:
            try:
                # Poll for messages (timeout prevents blocking forever)
                message_batch = self.consumer.poll(timeout_ms=1000)

                if not message_batch:
                    continue  # No messages, continue polling

                # Process each message in the batch
                for messages in message_batch.values():
                    for message in messages:
                        # Parse Debezium event
                        parsed_event = self.parse_debezium_event(message.value)

                        if parsed_event:
                            # Process the change event
                            self.process_change_event(parsed_event)

                # Commit offsets after processing batch
                self.consumer.commit()

            except KafkaError:
                continue
            except Exception:
                continue

        self.consumer.close()

def main():
    """
    Main entry point for the Kafka consumer application.

    Why separate main function?
    - Makes the module importable for testing
    - Provides clear entry point for container
    - Allows proper error handling at top level
    """
    consumer = DebeziumKafkaConsumer()
    consumer.start_consuming()

if __name__ == "__main__":
    main()