"""
Typesense Synchronization Service for MySQL-Debezium-Kafka-Typesense Pipeline

This module handles synchronization of data from MySQL (via Kafka) to Typesense search engine.

TYPESENSE ROLE IN PIPELINE:
- Provides fast, typo-tolerant search capabilities
- Indexes data from MySQL for real-time search
- Supports faceted search, filtering, and ranking
- Scales horizontally for high query loads

WHY TYPESENSE SYNC IS NEEDED:
- Transforms MySQL data format to Typesense document format
- Manages Typesense collections (equivalent to database tables)
- Handles schema mapping between MySQL and Typesense
- Provides error handling and retry logic for search operations
"""

import logging
import typesense
from tenacity import retry, stop_after_attempt, wait_exponential
from config import Config

logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class TypesenseSync:
    """
    Handles synchronization of data between MySQL and Typesense.

    This class manages:
    - Typesense client connection
    - Collection schema creation and management
    - Document CRUD operations (Create, Read, Update, Delete)
    - Data transformation between MySQL and Typesense formats
    - Error handling and retries
    """

    def __init__(self):
        """
        Initialize Typesense client and set up collections.

        Why initialize collections on startup?
        - Ensures search collections exist before data arrives
        - Validates Typesense connection early
        - Sets up proper schema for optimal search performance
        """
        self.client = self._create_client()
        self._setup_collections()

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def _create_client(self):
        """
        Create Typesense client with retry logic.

        Why retry logic?
        - Typesense container might not be ready when this starts
        - Network issues can cause temporary connection failures
        - Improves reliability in distributed environments
        """
        return typesense.Client(Config.get_typesense_config())

    def _setup_collections(self):
        """
        Create Typesense collections for all MySQL tables.

        Why create collections upfront?
        - Ensures collections exist before data arrives from Kafka
        - Validates schema definitions early
        - Avoids race conditions during data ingestion
        """
        # Define schemas for all tables
        schemas = [
            {
                'name': 'users',
                'fields': [
                    {'name': 'id', 'type': 'string'},
                    {'name': 'username', 'type': 'string'},
                    {'name': 'email', 'type': 'string'},
                    {'name': 'full_name', 'type': 'string'},
                    {'name': 'age', 'type': 'int32', 'optional': True},
                    {'name': 'city', 'type': 'string', 'optional': True},
                    {'name': 'created_at', 'type': 'string', 'optional': True},
                    {'name': 'updated_at', 'type': 'string', 'optional': True}
                ]
            },
            {
                'name': 'products',
                'fields': [
                    {'name': 'id', 'type': 'string'},
                    {'name': 'name', 'type': 'string'},
                    {'name': 'description', 'type': 'string', 'optional': True},
                    {'name': 'price', 'type': 'float'},
                    {'name': 'category', 'type': 'string'},
                    {'name': 'stock_quantity', 'type': 'int32'},
                    {'name': 'created_at', 'type': 'string', 'optional': True},
                    {'name': 'updated_at', 'type': 'string', 'optional': True}
                ]
            },
            {
                'name': 'orders',
                'fields': [
                    {'name': 'id', 'type': 'string'},
                    {'name': 'user_id', 'type': 'int32', 'optional': True},
                    {'name': 'product_id', 'type': 'int32', 'optional': True},
                    {'name': 'quantity', 'type': 'int32'},
                    {'name': 'item_price', 'type': 'float'},
                    {'name': 'total_price', 'type': 'float'},
                    {'name': 'order_status', 'type': 'string'},
                    {'name': 'created_at', 'type': 'string', 'optional': True},
                    {'name': 'updated_at', 'type': 'string', 'optional': True}
                ]
            }
        ]

        # Create collections if they don't exist
        for schema in schemas:
            try:
                self.client.collections[schema['name']].retrieve()
                logger.info(f"Collection '{schema['name']}' already exists")
            except typesense.exceptions.ObjectNotFound:
                self.client.collections.create(schema)
                logger.info(f"Created collection '{schema['name']}'")
            except Exception as e:
                logger.error(f"Error setting up collection '{schema['name']}': {e}")

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def upsert_document(self, table_name, document_data):
        """
        Insert or update a document in Typesense collection.

        UPSERT OPERATION:
        - If document exists (same ID), update it
        - If document doesn't exist, create it
        - This handles both INSERT and UPDATE operations from MySQL

        Why upsert instead of separate insert/update?
        - Simplifies code by handling both cases
        - Avoids errors when document already exists
        - Ensures data consistency between MySQL and Typesense
        """
        try:
            doc = self._transform_document(table_name, document_data)
            result = self.client.collections[table_name].documents.upsert(doc)
            logger.debug(f"Upserted document in {table_name}: {doc['id']}")
            return True
        except Exception as e:
            logger.error(f"Failed to upsert document in {table_name}: {e}")
            return False

    def _transform_document(self, table_name, document_data):
        """
        Transform MySQL document data to Typesense format.

        Why transformation needed?
        - Convert data types to match Typesense schema
        - Handle null values appropriately
        - Ensure ID is always a string
        """
        doc = document_data.copy()

        # Convert ID to string (required by Typesense)
        doc['id'] = str(doc['id'])

        # Handle table-specific transformations
        if table_name == 'products':
            # Convert price to float, handling Debezium's base64 encoded decimals
            if 'price' in doc and doc['price'] is not None:
                doc['price'] = self._decode_decimal_value(doc['price'], 'price')

        elif table_name == 'orders':
            # Convert prices to float, handling Debezium's base64 encoded decimals
            for price_field in ['item_price', 'total_price']:
                if price_field in doc and doc[price_field] is not None:
                    doc[price_field] = self._decode_decimal_value(doc[price_field], price_field)

        # Convert datetime objects to strings
        for key, value in doc.items():
            if hasattr(value, 'isoformat'):  # datetime object
                doc[key] = value.isoformat()
            elif value is None:
                # Remove null values for optional fields
                continue

        # Remove None values to avoid Typesense errors
        doc = {k: v for k, v in doc.items() if v is not None}

        return doc

    def _decode_decimal_value(self, value, field_name):
        """
        Decode Debezium's base64 encoded decimal values.

        Debezium encodes MySQL DECIMAL fields as base64 strings.
        This method attempts to decode them back to float values.
        """
        try:
            if isinstance(value, str):
                import base64
                import struct

                # Try to decode as base64
                try:
                    decoded_bytes = base64.b64decode(value)

                    # For MySQL DECIMAL, Debezium uses a specific encoding
                    # Let's try to interpret the bytes as a decimal value
                    if len(decoded_bytes) >= 4:
                        # Try different interpretations
                        try:
                            # Try as big-endian integer first
                            int_val = int.from_bytes(decoded_bytes, byteorder='big', signed=True)
                            # Scale it down (MySQL DECIMAL(10,2) means 2 decimal places)
                            float_val = int_val / 100.0
                            logger.debug(f"Decoded {field_name} '{value}' to {float_val}")
                            return float_val
                        except:
                            # Try as little-endian
                            int_val = int.from_bytes(decoded_bytes, byteorder='little', signed=True)
                            float_val = int_val / 100.0
                            logger.debug(f"Decoded {field_name} '{value}' to {float_val}")
                            return float_val
                    else:
                        # Short bytes, try direct conversion
                        return float(decoded_bytes.decode('utf-8'))

                except Exception as decode_error:
                    logger.debug(f"Base64 decode failed for {field_name}: {decode_error}")
                    # If base64 decoding fails, try direct string conversion
                    return float(value)
            else:
                # If it's not a string, try direct conversion
                return float(value)

        except (ValueError, TypeError) as e:
            logger.warning(f"Could not convert {field_name} '{value}' to float: {e}")
            return 0.0  # Default value

    @retry(
        stop=stop_after_attempt(Config.RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=Config.RETRY_DELAY)
    )
    def delete_document(self, table_name, document_id):
        """
        Delete a document from Typesense collection.

        DELETE OPERATION:
        - Removes document with specified ID from collection
        - Handles MySQL DELETE operations
        - Gracefully handles cases where document doesn't exist

        Why separate delete method?
        - DELETE operations require only document ID
        - Different error handling than upsert operations
        - Allows specific logging for delete operations
        """
        try:
            self.client.collections[table_name].documents[str(document_id)].delete()
            logger.debug(f"Deleted document from {table_name}: {document_id}")
            return True
        except typesense.exceptions.ObjectNotFound:
            logger.debug(f"Document not found for deletion in {table_name}: {document_id}")
            return True  # Consider it successful if document doesn't exist
        except Exception as e:
            logger.error(f"Failed to delete document from {table_name}: {e}")
            return False