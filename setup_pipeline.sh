#!/bin/bash

# MySQL-Debezium-Kafka-Typesense Pipeline Setup Script
# This script sets up and runs the complete CDC pipeline

set -e  # Exit on any error

echo "🚀 Starting MySQL-Debezium-Kafka-Typesense Pipeline Setup"
echo "========================================================"

# Function to check if a service is healthy
check_service_health() {
    local service_name=$1
    local health_check=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval $health_check > /dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 5
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Function to wait for Kafka topics to be created
wait_for_kafka_topics() {
    echo "⏳ Waiting for Kafka topics to be created..."
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        local topic_count=$(docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list 2>/dev/null | grep -c "dbserver1.testdb" || echo "0")
        
        if [ "$topic_count" -ge 3 ]; then
            echo "✅ Kafka topics are ready!"
            docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list | grep "dbserver1.testdb"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - Found $topic_count topics, waiting for 3..."
        sleep 5
        ((attempt++))
    done
    
    echo "❌ Kafka topics not created within expected time"
    return 1
}

# Step 1: Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down -v 2>/dev/null || true

# Step 2: Start infrastructure services
echo "🏗️  Starting infrastructure services (MySQL, Zookeeper, Kafka, Typesense)..."
docker-compose up -d mysql zookeeper kafka typesense

# Step 3: Wait for services to be ready
check_service_health "MySQL" "docker exec mysql mysqladmin ping -h localhost -u root -prootpassword"
check_service_health "Kafka" "docker exec kafka kafka-broker-api-versions --bootstrap-server localhost:9092"
check_service_health "Typesense" "curl -f http://localhost:8108/health"

# Step 4: Start Debezium
echo "🔄 Starting Debezium connector..."
docker-compose up -d debezium

check_service_health "Debezium" "curl -f http://localhost:8083/connectors"

# Step 5: Configure Debezium MySQL connector
echo "⚙️  Configuring Debezium MySQL connector..."
sleep 10  # Give Debezium time to fully start

curl -X POST http://localhost:8083/connectors \
  -H "Content-Type: application/json" \
  -d @debezium/mysql-connector.json

# Wait a moment for connector to initialize
sleep 5

# Check connector status
echo "🔍 Checking Debezium connector status..."
curl -s http://localhost:8083/connectors/mysql-connector/status | python3 -m json.tool

# Step 6: Wait for Kafka topics to be created
wait_for_kafka_topics

# Step 7: Start Python consumer
echo "🐍 Starting Python consumer..."
docker-compose up -d python-consumer

# Step 8: Wait for Python consumer to be ready
sleep 10

# Step 9: Verify all services are running
echo "🔍 Verifying all services are running..."
docker-compose ps

echo ""
echo "🎉 Pipeline setup complete!"
echo "=========================="
echo ""
echo "📊 Service URLs:"
echo "   MySQL: localhost:3306 (user: dbuser, password: dbpassword, database: testdb)"
echo "   Kafka: localhost:29092"
echo "   Debezium Connect: http://localhost:8083"
echo "   Typesense: http://localhost:8108 (API Key: xyz123)"
echo ""
echo "🧪 Quick test commands:"
echo "   # Check all services"
echo "   docker-compose ps"
echo ""
echo "   # View consumer logs"
echo "   docker-compose logs -f python-consumer"
echo ""
echo "   # Test database insert"
echo "   docker exec mysql mysql -u dbuser -pdbpassword testdb -e \"INSERT INTO users (username, email, full_name, age, city) VALUES ('testuser', '<EMAIL>', 'Test User', 25, 'New York');\""
echo ""
echo "   # Search in Typesense"
echo "   curl \"http://localhost:8108/collections/users/documents/search?q=testuser&query_by=username\" -H \"X-TYPESENSE-API-KEY: xyz123\""
echo ""
echo "✅ The complete MySQL → Debezium → Kafka → Python → Typesense pipeline is now operational!"
